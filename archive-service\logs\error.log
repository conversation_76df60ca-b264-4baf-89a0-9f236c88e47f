{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:44:32:4432"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:45:35:4535"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:49:39:4939"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-15 18:23:43:2343"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 18:49:06:496"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-15 19:02:14:214"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-15 19:04:12:412"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 03:57:46:5746"}
{"database":"atma_db","error":"connect ECONNREFUSED **********:5432","host":"postgres","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 21:50:36:5036"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 08:21:26:2126"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 13:16:18:1618"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 13:18:03:183"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 13:20:59:2059"}
{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:132:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-16 23:45:41:4541"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:50:28:5028","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:50:28:5028"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:51:15:5115","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:51:15:5115"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:53:02:532","userId":"550e8400-e29b-41d4-a716-446655440000"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:53:02:532"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:54:53:5453","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:54:53:5453"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:54:58:5458","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:54:58:5458"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:55:08:558","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:55:08:558"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:55:28:5528","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:55:28:5528"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:55:47:5547","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:55:47:5547"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:02:562","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:02:562"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:22:5622","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:22:5622"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:28:5628","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:28:5628"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:39:5639","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:39:5639"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:44:5644","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:44:5644"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:54:5654","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:54:5654"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:14:5714","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:14:5714"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:34:5734","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:35:5735"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:40:5740","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:40:5740"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:50:5750","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:50:5750"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:03:583","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:03:583"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:10:5810","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:10:5810"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:28:5828","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:28:5828"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:33:5833","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:33:5833"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:43:5843","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:43:5843"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:46:5846","userId":"550e8400-e29b-41d4-a716-446655440000"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:46:5846"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:03:593","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:03:593"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:22:5922","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:22:5922"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:27:5927","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:27:5927"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:37:5937","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:37:5937"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:57:5957","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:57:5957"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:25:3525","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:25:3525"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:30:3530","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:30:3530"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:40:3540","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:40:3540"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:00:360","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:00:360"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:40:3640","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:40:3640"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:45:3645","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:45:3645"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:55:3655","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:55:3655"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:37:15:3715","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:37:15:3715"}
{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:132:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-17 11:01:40:140"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:05:35","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:05:35"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:10:310","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:10:310"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:20:320","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:20:320"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:40:340","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:40:340"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:04:05:45","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:04:05:45"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:04:10:410","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:04:10:410"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:13:57:1357","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:13:57:1357"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:02:142","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:02:142"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:12:1412","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:12:1412"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:32:1432","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:32:1432"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:41:1441","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:41:1441"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:46:1446","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:46:1446"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:56:1456","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:56:1456"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:00:150","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:00:150"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:05:155","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:05:155"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:15:1515","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:15:1515"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:35:1535","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:35:1535"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:39:1539","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:39:1539"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:44:1544","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:44:1544"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:54:1554","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:54:1554"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:14:1614","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:14:1614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:14:1614","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:14:1614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:19:1619","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:19:1619"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:29:1629","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:29:1629"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:42:1642","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:42:1642"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:47:1647","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:47:1647"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:49:1649","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:49:1649"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:57:1657","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:57:1657"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:17:57:1757","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:17:57:1757"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:18:02:182","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:18:02:182"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:18:12:1812","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:18:12:1812"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:18:32:1832","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:18:32:1832"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:19:31:1931","userId":"550e8400-e29b-41d4-a716-446655440001"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:19:31:1931"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Database insert error details:","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(550e8400-e29b-41d4-a716-446655440001) is not present in table \"users\".","file":"ri_triggers.c","length":318,"line":"2610","name":"error","parameters":["a946af89-d825-42d7-b4ed-0c906c076c53","550e8400-e29b-41d4-a716-446655440001","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":78,\"judgment\":70,\"loveOfLearning\":65,\"perspective\":75,\"bravery\":60,\"perseverance\":80,\"honesty\":90,\"zest\":70,\"love\":85,\"kindness\":88,\"socialIntelligence\":75,\"teamwork\":82,\"fairness\":85,\"leadership\":70,\"forgiveness\":75,\"humility\":65,\"prudence\":70,\"selfRegulation\":75,\"appreciationOfBeauty\":80,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Analytical Innovator\",\"shortSummary\":\"Test persona profile for debugging\",\"strengths\":[\"Analytical thinking\",\"Problem solving\",\"Creative innovation\"],\"weaknesses\":[\"Perfectionism\",\"Overthinking\",\"Impatience\"],\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Software Engineer\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Research Analyst\",\"careerProspect\":{\"jobAvailability\":\"moderate\",\"salaryPotential\":\"moderate\",\"careerProgression\":\"moderate\",\"industryGrowth\":\"moderate\",\"skillDevelopment\":\"high\"}}],\"insights\":[\"Focus on practical applications\",\"Develop communication skills\",\"Build leadership capabilities\"],\"workEnvironment\":\"Collaborative and innovative environment\",\"roleModel\":[\"Marie Curie\",\"Albert Einstein\",\"Steve Jobs\",\"Katherine Johnson\"]}","completed","2025-07-17 04:20:41.523 +00:00","2025-07-17 04:20:41.524 +00:00"],"routine":"ri_ReportViolation","schema":"archive","severity":"ERROR","sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","table":"analysis_results"},"parameters":["a946af89-d825-42d7-b4ed-0c906c076c53","550e8400-e29b-41d4-a716-446655440001","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":78,\"judgment\":70,\"loveOfLearning\":65,\"perspective\":75,\"bravery\":60,\"perseverance\":80,\"honesty\":90,\"zest\":70,\"love\":85,\"kindness\":88,\"socialIntelligence\":75,\"teamwork\":82,\"fairness\":85,\"leadership\":70,\"forgiveness\":75,\"humility\":65,\"prudence\":70,\"selfRegulation\":75,\"appreciationOfBeauty\":80,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Analytical Innovator\",\"shortSummary\":\"Test persona profile for debugging\",\"strengths\":[\"Analytical thinking\",\"Problem solving\",\"Creative innovation\"],\"weaknesses\":[\"Perfectionism\",\"Overthinking\",\"Impatience\"],\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Software Engineer\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Research Analyst\",\"careerProspect\":{\"jobAvailability\":\"moderate\",\"salaryPotential\":\"moderate\",\"careerProgression\":\"moderate\",\"industryGrowth\":\"moderate\",\"skillDevelopment\":\"high\"}}],\"insights\":[\"Focus on practical applications\",\"Develop communication skills\",\"Build leadership capabilities\"],\"workEnvironment\":\"Collaborative and innovative environment\",\"roleModel\":[\"Marie Curie\",\"Albert Einstein\",\"Steve Jobs\",\"Katherine Johnson\"]}","completed","2025-07-17 04:20:41.523 +00:00","2025-07-17 04:20:41.524 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","timestamp":"2025-07-17 11:20:41:2041"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:20:41:2041","userId":"550e8400-e29b-41d4-a716-446655440001"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:20:41:2041"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Database insert error details:","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(550e8400-e29b-41d4-a716-446655440001) is not present in table \"users\".","file":"ri_triggers.c","length":318,"line":"2610","name":"error","parameters":["c9f7324a-f7f1-4f0d-80d2-d001cac0b287","550e8400-e29b-41d4-a716-446655440001","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":78,\"judgment\":70,\"loveOfLearning\":65,\"perspective\":75,\"bravery\":60,\"perseverance\":80,\"honesty\":90,\"zest\":70,\"love\":85,\"kindness\":88,\"socialIntelligence\":75,\"teamwork\":82,\"fairness\":85,\"leadership\":70,\"forgiveness\":75,\"humility\":65,\"prudence\":70,\"selfRegulation\":75,\"appreciationOfBeauty\":80,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Analytical Innovator\",\"shortSummary\":\"Test persona profile for debugging\",\"strengths\":[\"Analytical thinking\",\"Problem solving\",\"Creative innovation\"],\"weaknesses\":[\"Perfectionism\",\"Overthinking\",\"Impatience\"],\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Software Engineer\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Research Analyst\",\"careerProspect\":{\"jobAvailability\":\"moderate\",\"salaryPotential\":\"moderate\",\"careerProgression\":\"moderate\",\"industryGrowth\":\"moderate\",\"skillDevelopment\":\"high\"}}],\"insights\":[\"Focus on practical applications\",\"Develop communication skills\",\"Build leadership capabilities\"],\"workEnvironment\":\"Collaborative and innovative environment\",\"roleModel\":[\"Marie Curie\",\"Albert Einstein\",\"Steve Jobs\",\"Katherine Johnson\"]}","completed","2025-07-17 04:23:38.240 +00:00","2025-07-17 04:23:38.240 +00:00"],"routine":"ri_ReportViolation","schema":"archive","severity":"ERROR","sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","table":"analysis_results"},"parameters":["c9f7324a-f7f1-4f0d-80d2-d001cac0b287","550e8400-e29b-41d4-a716-446655440001","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":78,\"judgment\":70,\"loveOfLearning\":65,\"perspective\":75,\"bravery\":60,\"perseverance\":80,\"honesty\":90,\"zest\":70,\"love\":85,\"kindness\":88,\"socialIntelligence\":75,\"teamwork\":82,\"fairness\":85,\"leadership\":70,\"forgiveness\":75,\"humility\":65,\"prudence\":70,\"selfRegulation\":75,\"appreciationOfBeauty\":80,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Analytical Innovator\",\"shortSummary\":\"Test persona profile for debugging\",\"strengths\":[\"Analytical thinking\",\"Problem solving\",\"Creative innovation\"],\"weaknesses\":[\"Perfectionism\",\"Overthinking\",\"Impatience\"],\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Software Engineer\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Research Analyst\",\"careerProspect\":{\"jobAvailability\":\"moderate\",\"salaryPotential\":\"moderate\",\"careerProgression\":\"moderate\",\"industryGrowth\":\"moderate\",\"skillDevelopment\":\"high\"}}],\"insights\":[\"Focus on practical applications\",\"Develop communication skills\",\"Build leadership capabilities\"],\"workEnvironment\":\"Collaborative and innovative environment\",\"roleModel\":[\"Marie Curie\",\"Albert Einstein\",\"Steve Jobs\",\"Katherine Johnson\"]}","completed","2025-07-17 04:23:38.240 +00:00","2025-07-17 04:23:38.240 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","timestamp":"2025-07-17 11:23:38:2338"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:23:38:2338","userId":"550e8400-e29b-41d4-a716-446655440001"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:23:38:2338"}
{"level":"error","message":"Database insert error details: insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(550e8400-e29b-41d4-a716-446655440001) is not present in table \"users\".","message":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","table":"analysis_results"},"parameters":["a618a35a-b15e-40fa-9994-28bf7eb9fc04","550e8400-e29b-41d4-a716-446655440001","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":78,\"judgment\":70,\"loveOfLearning\":65,\"perspective\":75,\"bravery\":60,\"perseverance\":80,\"honesty\":90,\"zest\":70,\"love\":85,\"kindness\":88,\"socialIntelligence\":75,\"teamwork\":82,\"fairness\":85,\"leadership\":70,\"forgiveness\":75,\"humility\":65,\"prudence\":70,\"selfRegulation\":75,\"appreciationOfBeauty\":80,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Analytical Innovator\",\"shortSummary\":\"Test persona profile for debugging\",\"strengths\":[\"Analytical thinking\",\"Problem solving\",\"Creative innovation\"],\"weaknesses\":[\"Perfectionism\",\"Overthinking\",\"Impatience\"],\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Software Engineer\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Research Analyst\",\"careerProspect\":{\"jobAvailability\":\"moderate\",\"salaryPotential\":\"moderate\",\"careerProgression\":\"moderate\",\"industryGrowth\":\"moderate\",\"skillDevelopment\":\"high\"}}],\"insights\":[\"Focus on practical applications\",\"Develop communication skills\",\"Build leadership capabilities\"],\"workEnvironment\":\"Collaborative and innovative environment\",\"roleModel\":[\"Marie Curie\",\"Albert Einstein\",\"Steve Jobs\",\"Katherine Johnson\"]}","completed","2025-07-17 04:26:18.879 +00:00","2025-07-17 04:26:18.880 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:26:18:2618"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:26:18:2618","userId":"550e8400-e29b-41d4-a716-446655440001"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:26:18:2618"}
{"level":"error","message":"Database insert error details: insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(e9034651-8aa9-471b-97c7-71babc1ac589) is not present in table \"users\".","message":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","table":"analysis_results"},"parameters":["505bc419-7807-47db-a708-524ef499f9d9","e9034651-8aa9-471b-97c7-71babc1ac589","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":90,\"judgment\":75,\"loveOfLearning\":80,\"perspective\":70,\"bravery\":65,\"perseverance\":85,\"honesty\":90,\"zest\":75,\"love\":80,\"kindness\":85,\"socialIntelligence\":70,\"teamwork\":80,\"fairness\":85,\"leadership\":75,\"forgiveness\":70,\"humility\":65,\"prudence\":75,\"selfRegulation\":80,\"appreciationOfBeauty\":70,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Principled Innovator\",\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Research & Development Engineer\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Management Consultant (Strategy/Operations)\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Software Architect\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Forensic Analyst\",\"careerProspect\":{\"careerProgression\":\"moderate\",\"industryGrowth\":\"moderate\",\"jobAvailability\":\"moderate\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"high\"}}],\"insights\":[\"Kembangkan keterampilan komunikasi strategis untuk menyampaikan ide-ide yang lugas dan jujur tanpa mengorbankan penerimaan dari rekan kerja atau atasan.\",\"Fokus pada peran yang memungkinkan Anda mendalami masalah kompleks dan memanfaatkan kapasitas analitis serta rasa ingin tahu yang tinggi.\",\"Cari lingkungan kerja yang menghargai objektivitas, inovasi, dan debat intelektual yang konstruktif daripada hanya mengutamakan harmoni sosial.\",\"Sadarilah bahwa pendekatan Anda yang langsung dan terkadang skeptis mungkin perlu disesuaikan untuk situasi yang membutuhkan diplomasi atau sensitivitas interpersonal.\",\"Teruslah berinvestasi dalam pembelajaran berkelanjutan dan eksplorasi ide-ide baru untuk menjaga vitalitas intelektual dan relevansi profesional.\"],\"roleModel\":[\"Elon Musk\",\"Marie Curie\",\"Bill Gates\",\"Sheryl Sandberg\",\"Ada Lovelace\"],\"shortSummary\":\"Individu ini adalah pemikir yang sangat analitis dan inovatif, didorong oleh rasa ingin tahu yang tak terbatas dan hasrat kuat untuk pembelajaran berkelanjutan. Mereka unggul dalam membedah masalah kompleks dan mengembangkan solusi kreatif, seringkali tidak konvensional. Pendekatan mereka ditandai oleh integritas tinggi dan kegigihan, memastikan ketelitian dan eksekusi yang etis. Meskipun mereka cenderung lugas dan berprinsip dalam interaksi, mereka sangat menghargai keadilan dan kolaborasi yang efektif.\",\"strengths\":[\"Kemampuan analitis dan pemecahan masalah yang luar biasa.\",\"Rasa ingin tahu intelektual yang tinggi dan semangat untuk belajar berkelanjutan.\",\"Kompas etika yang kuat dan kejujuran yang teguh.\",\"Pendekatan yang sangat inovatif dan kreatif.\",\"Ketekunan dan disiplin diri yang luar biasa dalam mencapai tujuan.\"],\"weaknesses\":[\"Kecenderungan untuk bersikap lugas yang mungkin dianggap blak-blakan karena Agreeableness yang rendah.\",\"Potensi untuk menjadi terlalu skeptis atau kritis, terkadang menghambat konsensus cepat.\",\"Mungkin kesulitan beradaptasi di lingkungan yang sangat tidak terstruktur atau ambigu secara sosial.\",\"Dapat memprioritaskan logika dan fakta di atas nuansa sosial, berpotensi memengaruhi dinamika interpersonal.\",\"Mungkin kurang tertarik pada interaksi sosial murni tanpa tujuan yang jelas.\"],\"workEnvironment\":\"Lingkungan kerja yang ideal adalah yang menghargai ketelitian intelektual, inovasi, dan pemecahan masalah secara objektif. Lingkungan ini harus menawarkan kesempatan untuk fokus mendalam dan pekerjaan independen, sambil juga menyediakan jalur terstruktur untuk kolaborasi dalam proyek-proyek kompleks. Budaya yang menghargai umpan balik langsung, keputusan berdasarkan merit, dan perilaku etis akan sangat cocok. Lebih sedikit penekanan pada interaksi sosial yang dangkal dan lebih banyak pada kontribusi substantif.\"}","completed","2025-07-17 04:28:10.377 +00:00","2025-07-17 04:28:10.377 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:28:10:2810"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:28:10:2810","userId":"e9034651-8aa9-471b-97c7-71babc1ac589"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:28:10:2810"}
{"level":"error","message":"Database insert error details: insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(e9034651-8aa9-471b-97c7-71babc1ac589) is not present in table \"users\".","message":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","table":"analysis_results"},"parameters":["eb5185f6-3b07-465c-991e-d33ca30e9496","e9034651-8aa9-471b-97c7-71babc1ac589","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":90,\"judgment\":75,\"loveOfLearning\":80,\"perspective\":70,\"bravery\":65,\"perseverance\":85,\"honesty\":90,\"zest\":75,\"love\":80,\"kindness\":85,\"socialIntelligence\":70,\"teamwork\":80,\"fairness\":85,\"leadership\":75,\"forgiveness\":70,\"humility\":65,\"prudence\":75,\"selfRegulation\":80,\"appreciationOfBeauty\":70,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Principled Innovator\",\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Research & Development Engineer\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Management Consultant (Strategy/Operations)\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Software Architect\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Forensic Analyst\",\"careerProspect\":{\"careerProgression\":\"moderate\",\"industryGrowth\":\"moderate\",\"jobAvailability\":\"moderate\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"high\"}}],\"insights\":[\"Kembangkan keterampilan komunikasi strategis untuk menyampaikan ide-ide yang lugas dan jujur tanpa mengorbankan penerimaan dari rekan kerja atau atasan.\",\"Fokus pada peran yang memungkinkan Anda mendalami masalah kompleks dan memanfaatkan kapasitas analitis serta rasa ingin tahu yang tinggi.\",\"Cari lingkungan kerja yang menghargai objektivitas, inovasi, dan debat intelektual yang konstruktif daripada hanya mengutamakan harmoni sosial.\",\"Sadarilah bahwa pendekatan Anda yang langsung dan terkadang skeptis mungkin perlu disesuaikan untuk situasi yang membutuhkan diplomasi atau sensitivitas interpersonal.\",\"Teruslah berinvestasi dalam pembelajaran berkelanjutan dan eksplorasi ide-ide baru untuk menjaga vitalitas intelektual dan relevansi profesional.\"],\"roleModel\":[\"Elon Musk\",\"Marie Curie\",\"Bill Gates\",\"Sheryl Sandberg\",\"Ada Lovelace\"],\"shortSummary\":\"Individu ini adalah pemikir yang sangat analitis dan inovatif, didorong oleh rasa ingin tahu yang tak terbatas dan hasrat kuat untuk pembelajaran berkelanjutan. Mereka unggul dalam membedah masalah kompleks dan mengembangkan solusi kreatif, seringkali tidak konvensional. Pendekatan mereka ditandai oleh integritas tinggi dan kegigihan, memastikan ketelitian dan eksekusi yang etis. Meskipun mereka cenderung lugas dan berprinsip dalam interaksi, mereka sangat menghargai keadilan dan kolaborasi yang efektif.\",\"strengths\":[\"Kemampuan analitis dan pemecahan masalah yang luar biasa.\",\"Rasa ingin tahu intelektual yang tinggi dan semangat untuk belajar berkelanjutan.\",\"Kompas etika yang kuat dan kejujuran yang teguh.\",\"Pendekatan yang sangat inovatif dan kreatif.\",\"Ketekunan dan disiplin diri yang luar biasa dalam mencapai tujuan.\"],\"weaknesses\":[\"Kecenderungan untuk bersikap lugas yang mungkin dianggap blak-blakan karena Agreeableness yang rendah.\",\"Potensi untuk menjadi terlalu skeptis atau kritis, terkadang menghambat konsensus cepat.\",\"Mungkin kesulitan beradaptasi di lingkungan yang sangat tidak terstruktur atau ambigu secara sosial.\",\"Dapat memprioritaskan logika dan fakta di atas nuansa sosial, berpotensi memengaruhi dinamika interpersonal.\",\"Mungkin kurang tertarik pada interaksi sosial murni tanpa tujuan yang jelas.\"],\"workEnvironment\":\"Lingkungan kerja yang ideal adalah yang menghargai ketelitian intelektual, inovasi, dan pemecahan masalah secara objektif. Lingkungan ini harus menawarkan kesempatan untuk fokus mendalam dan pekerjaan independen, sambil juga menyediakan jalur terstruktur untuk kolaborasi dalam proyek-proyek kompleks. Budaya yang menghargai umpan balik langsung, keputusan berdasarkan merit, dan perilaku etis akan sangat cocok. Lebih sedikit penekanan pada interaksi sosial yang dangkal dan lebih banyak pada kontribusi substantif.\"}","completed","2025-07-17 04:28:15.420 +00:00","2025-07-17 04:28:15.420 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:28:15:2815"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:28:15:2815","userId":"e9034651-8aa9-471b-97c7-71babc1ac589"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:28:15:2815"}
{"level":"error","message":"Database insert error details: insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(e9034651-8aa9-471b-97c7-71babc1ac589) is not present in table \"users\".","message":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","table":"analysis_results"},"parameters":["e18e58b9-66dd-41ed-b5ef-0e3a67ca1d7f","e9034651-8aa9-471b-97c7-71babc1ac589","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":90,\"judgment\":75,\"loveOfLearning\":80,\"perspective\":70,\"bravery\":65,\"perseverance\":85,\"honesty\":90,\"zest\":75,\"love\":80,\"kindness\":85,\"socialIntelligence\":70,\"teamwork\":80,\"fairness\":85,\"leadership\":75,\"forgiveness\":70,\"humility\":65,\"prudence\":75,\"selfRegulation\":80,\"appreciationOfBeauty\":70,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Principled Innovator\",\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Research & Development Engineer\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Management Consultant (Strategy/Operations)\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Software Architect\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Forensic Analyst\",\"careerProspect\":{\"careerProgression\":\"moderate\",\"industryGrowth\":\"moderate\",\"jobAvailability\":\"moderate\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"high\"}}],\"insights\":[\"Kembangkan keterampilan komunikasi strategis untuk menyampaikan ide-ide yang lugas dan jujur tanpa mengorbankan penerimaan dari rekan kerja atau atasan.\",\"Fokus pada peran yang memungkinkan Anda mendalami masalah kompleks dan memanfaatkan kapasitas analitis serta rasa ingin tahu yang tinggi.\",\"Cari lingkungan kerja yang menghargai objektivitas, inovasi, dan debat intelektual yang konstruktif daripada hanya mengutamakan harmoni sosial.\",\"Sadarilah bahwa pendekatan Anda yang langsung dan terkadang skeptis mungkin perlu disesuaikan untuk situasi yang membutuhkan diplomasi atau sensitivitas interpersonal.\",\"Teruslah berinvestasi dalam pembelajaran berkelanjutan dan eksplorasi ide-ide baru untuk menjaga vitalitas intelektual dan relevansi profesional.\"],\"roleModel\":[\"Elon Musk\",\"Marie Curie\",\"Bill Gates\",\"Sheryl Sandberg\",\"Ada Lovelace\"],\"shortSummary\":\"Individu ini adalah pemikir yang sangat analitis dan inovatif, didorong oleh rasa ingin tahu yang tak terbatas dan hasrat kuat untuk pembelajaran berkelanjutan. Mereka unggul dalam membedah masalah kompleks dan mengembangkan solusi kreatif, seringkali tidak konvensional. Pendekatan mereka ditandai oleh integritas tinggi dan kegigihan, memastikan ketelitian dan eksekusi yang etis. Meskipun mereka cenderung lugas dan berprinsip dalam interaksi, mereka sangat menghargai keadilan dan kolaborasi yang efektif.\",\"strengths\":[\"Kemampuan analitis dan pemecahan masalah yang luar biasa.\",\"Rasa ingin tahu intelektual yang tinggi dan semangat untuk belajar berkelanjutan.\",\"Kompas etika yang kuat dan kejujuran yang teguh.\",\"Pendekatan yang sangat inovatif dan kreatif.\",\"Ketekunan dan disiplin diri yang luar biasa dalam mencapai tujuan.\"],\"weaknesses\":[\"Kecenderungan untuk bersikap lugas yang mungkin dianggap blak-blakan karena Agreeableness yang rendah.\",\"Potensi untuk menjadi terlalu skeptis atau kritis, terkadang menghambat konsensus cepat.\",\"Mungkin kesulitan beradaptasi di lingkungan yang sangat tidak terstruktur atau ambigu secara sosial.\",\"Dapat memprioritaskan logika dan fakta di atas nuansa sosial, berpotensi memengaruhi dinamika interpersonal.\",\"Mungkin kurang tertarik pada interaksi sosial murni tanpa tujuan yang jelas.\"],\"workEnvironment\":\"Lingkungan kerja yang ideal adalah yang menghargai ketelitian intelektual, inovasi, dan pemecahan masalah secara objektif. Lingkungan ini harus menawarkan kesempatan untuk fokus mendalam dan pekerjaan independen, sambil juga menyediakan jalur terstruktur untuk kolaborasi dalam proyek-proyek kompleks. Budaya yang menghargai umpan balik langsung, keputusan berdasarkan merit, dan perilaku etis akan sangat cocok. Lebih sedikit penekanan pada interaksi sosial yang dangkal dan lebih banyak pada kontribusi substantif.\"}","completed","2025-07-17 04:28:25.435 +00:00","2025-07-17 04:28:25.435 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:28:25:2825"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:28:25:2825","userId":"e9034651-8aa9-471b-97c7-71babc1ac589"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:28:25:2825"}
{"level":"error","message":"Database insert error details: insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(e9034651-8aa9-471b-97c7-71babc1ac589) is not present in table \"users\".","message":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","table":"analysis_results"},"parameters":["de4e4311-64e9-4a65-9dc6-17eebbe0ef44","e9034651-8aa9-471b-97c7-71babc1ac589","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":90,\"judgment\":75,\"loveOfLearning\":80,\"perspective\":70,\"bravery\":65,\"perseverance\":85,\"honesty\":90,\"zest\":75,\"love\":80,\"kindness\":85,\"socialIntelligence\":70,\"teamwork\":80,\"fairness\":85,\"leadership\":75,\"forgiveness\":70,\"humility\":65,\"prudence\":75,\"selfRegulation\":80,\"appreciationOfBeauty\":70,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Principled Innovator\",\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Research & Development Engineer\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Management Consultant (Strategy/Operations)\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Software Architect\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Forensic Analyst\",\"careerProspect\":{\"careerProgression\":\"moderate\",\"industryGrowth\":\"moderate\",\"jobAvailability\":\"moderate\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"high\"}}],\"insights\":[\"Kembangkan keterampilan komunikasi strategis untuk menyampaikan ide-ide yang lugas dan jujur tanpa mengorbankan penerimaan dari rekan kerja atau atasan.\",\"Fokus pada peran yang memungkinkan Anda mendalami masalah kompleks dan memanfaatkan kapasitas analitis serta rasa ingin tahu yang tinggi.\",\"Cari lingkungan kerja yang menghargai objektivitas, inovasi, dan debat intelektual yang konstruktif daripada hanya mengutamakan harmoni sosial.\",\"Sadarilah bahwa pendekatan Anda yang langsung dan terkadang skeptis mungkin perlu disesuaikan untuk situasi yang membutuhkan diplomasi atau sensitivitas interpersonal.\",\"Teruslah berinvestasi dalam pembelajaran berkelanjutan dan eksplorasi ide-ide baru untuk menjaga vitalitas intelektual dan relevansi profesional.\"],\"roleModel\":[\"Elon Musk\",\"Marie Curie\",\"Bill Gates\",\"Sheryl Sandberg\",\"Ada Lovelace\"],\"shortSummary\":\"Individu ini adalah pemikir yang sangat analitis dan inovatif, didorong oleh rasa ingin tahu yang tak terbatas dan hasrat kuat untuk pembelajaran berkelanjutan. Mereka unggul dalam membedah masalah kompleks dan mengembangkan solusi kreatif, seringkali tidak konvensional. Pendekatan mereka ditandai oleh integritas tinggi dan kegigihan, memastikan ketelitian dan eksekusi yang etis. Meskipun mereka cenderung lugas dan berprinsip dalam interaksi, mereka sangat menghargai keadilan dan kolaborasi yang efektif.\",\"strengths\":[\"Kemampuan analitis dan pemecahan masalah yang luar biasa.\",\"Rasa ingin tahu intelektual yang tinggi dan semangat untuk belajar berkelanjutan.\",\"Kompas etika yang kuat dan kejujuran yang teguh.\",\"Pendekatan yang sangat inovatif dan kreatif.\",\"Ketekunan dan disiplin diri yang luar biasa dalam mencapai tujuan.\"],\"weaknesses\":[\"Kecenderungan untuk bersikap lugas yang mungkin dianggap blak-blakan karena Agreeableness yang rendah.\",\"Potensi untuk menjadi terlalu skeptis atau kritis, terkadang menghambat konsensus cepat.\",\"Mungkin kesulitan beradaptasi di lingkungan yang sangat tidak terstruktur atau ambigu secara sosial.\",\"Dapat memprioritaskan logika dan fakta di atas nuansa sosial, berpotensi memengaruhi dinamika interpersonal.\",\"Mungkin kurang tertarik pada interaksi sosial murni tanpa tujuan yang jelas.\"],\"workEnvironment\":\"Lingkungan kerja yang ideal adalah yang menghargai ketelitian intelektual, inovasi, dan pemecahan masalah secara objektif. Lingkungan ini harus menawarkan kesempatan untuk fokus mendalam dan pekerjaan independen, sambil juga menyediakan jalur terstruktur untuk kolaborasi dalam proyek-proyek kompleks. Budaya yang menghargai umpan balik langsung, keputusan berdasarkan merit, dan perilaku etis akan sangat cocok. Lebih sedikit penekanan pada interaksi sosial yang dangkal dan lebih banyak pada kontribusi substantif.\"}","completed","2025-07-17 04:28:45.453 +00:00","2025-07-17 04:28:45.453 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:28:45:2845"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:28:45:2845","userId":"e9034651-8aa9-471b-97c7-71babc1ac589"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:28:45:2845"}
{"level":"error","message":"Database insert error details: insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(e9034651-8aa9-471b-97c7-71babc1ac589) is not present in table \"users\".","message":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","table":"analysis_results"},"parameters":["cc93d859-2506-4c72-a458-a410b9154afa","e9034651-8aa9-471b-97c7-71babc1ac589","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":90,\"judgment\":75,\"loveOfLearning\":80,\"perspective\":70,\"bravery\":65,\"perseverance\":85,\"honesty\":90,\"zest\":75,\"love\":80,\"kindness\":85,\"socialIntelligence\":70,\"teamwork\":80,\"fairness\":85,\"leadership\":75,\"forgiveness\":70,\"humility\":65,\"prudence\":75,\"selfRegulation\":80,\"appreciationOfBeauty\":70,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Principled Investigator\",\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Research & Development Engineer\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Management Consultant (Strategy/Operations)\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Cybersecurity Analyst\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Systems Architect\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}}],\"insights\":[\"Prioritaskan pengembangan kemampuan komunikasi persuasif untuk menyeimbangkan kecenderungan direktif Anda, terutama dalam konteks kolaborasi antar tim.\",\"Cari lingkungan kerja yang memfasilitasi eksplorasi intelektual mendalam dan implementasi solusi praktis, bukan hanya teori semata.\",\"Manfaatkan rasa ingin tahu dan kecintaan Anda pada pembelajaran untuk terus menguasai bidang-bidang baru yang kompleks dan multidisiplin.\",\"Ambil peran kepemimpinan yang membutuhkan analisis kritis, pemecahan masalah yang inovatif, dan komitmen kuat terhadap prinsip etika.\",\"Latih diri untuk menerima dan mengintegrasikan perspektif yang berbeda, bahkan jika pada awalnya tidak selaras dengan kerangka logis Anda sendiri.\"],\"roleModel\":[\"Marie Curie\",\"Alan Turing\",\"Ruth Bader Ginsburg\",\"Sheryl Sandberg\",\"Elon Musk\"],\"shortSummary\":\"Individu ini secara fundamental adalah pemikir analitis yang didorong oleh rasa ingin tahu yang tinggi dan keinginan untuk memahami sistem yang kompleks. Mereka memiliki kapasitas luar biasa untuk inovasi dan pemecahan masalah, tidak hanya secara teoritis tetapi juga dengan kemampuan untuk mengimplementasikan solusi yang efektif dan praktis. Disiplin, integritas, dan ketahanan adalah ciri khas yang menopang pendekatan mereka dalam menghadapi tantangan.\",\"strengths\":[\"Kemampuan analisis dan pemecahan masalah yang luar biasa.\",\"Dorongan kuat untuk pembelajaran berkelanjutan dan eksplorasi intelektual.\",\"Kapasitas tinggi untuk inovasi dan pemikiran kreatif.\",\"Ketahanan dan ketekunan dalam menghadapi rintangan.\",\"Integritas dan komitmen terhadap prinsip etika.\"],\"weaknesses\":[\"Potensi untuk bersikap terlalu lugas atau blak-blakan dalam interaksi interpersonal.\",\"Cenderung kurang nyaman dalam lingkungan yang sangat tidak terstruktur atau terlalu berorientasi sosial.\",\"Mungkin terlalu fokus pada detail, berpotensi memperlambat pengambilan keputusan di situasi yang membutuhkan kecepatan.\",\"Kurang sesuai untuk peran yang menuntut tingkat empati emosional yang tinggi atau konsensus interpersonal yang konstan.\"],\"workEnvironment\":\"Lingkungan kerja ideal adalah tempat yang mempromosikan pemikiran mendalam, analisis data, dan inovasi, namun tetap menyediakan struktur dan kejelasan tujuan. Kolaborasi dihargai dalam konteks penyelesaian tugas dan proyek, bukan semata-mata interaksi sosial yang ekstensif. Penting bagi mereka untuk berada di tempat yang menghargai keahlian, keputusan berbasis data, serta menjunjung tinggi etika dan integritas dalam praktik profesional.\"}","completed","2025-07-17 04:29:09.306 +00:00","2025-07-17 04:29:09.306 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:29:09:299"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:29:09:299","userId":"e9034651-8aa9-471b-97c7-71babc1ac589"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:29:09:299"}
{"level":"error","message":"Database insert error details: insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(e9034651-8aa9-471b-97c7-71babc1ac589) is not present in table \"users\".","message":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","table":"analysis_results"},"parameters":["6dc04229-bfa3-4412-95e8-35fa313e1ed8","e9034651-8aa9-471b-97c7-71babc1ac589","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":90,\"judgment\":75,\"loveOfLearning\":80,\"perspective\":70,\"bravery\":65,\"perseverance\":85,\"honesty\":90,\"zest\":75,\"love\":80,\"kindness\":85,\"socialIntelligence\":70,\"teamwork\":80,\"fairness\":85,\"leadership\":75,\"forgiveness\":70,\"humility\":65,\"prudence\":75,\"selfRegulation\":80,\"appreciationOfBeauty\":70,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Principled Investigator\",\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Research & Development Engineer\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Management Consultant (Strategy/Operations)\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Cybersecurity Analyst\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Systems Architect\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}}],\"insights\":[\"Prioritaskan pengembangan kemampuan komunikasi persuasif untuk menyeimbangkan kecenderungan direktif Anda, terutama dalam konteks kolaborasi antar tim.\",\"Cari lingkungan kerja yang memfasilitasi eksplorasi intelektual mendalam dan implementasi solusi praktis, bukan hanya teori semata.\",\"Manfaatkan rasa ingin tahu dan kecintaan Anda pada pembelajaran untuk terus menguasai bidang-bidang baru yang kompleks dan multidisiplin.\",\"Ambil peran kepemimpinan yang membutuhkan analisis kritis, pemecahan masalah yang inovatif, dan komitmen kuat terhadap prinsip etika.\",\"Latih diri untuk menerima dan mengintegrasikan perspektif yang berbeda, bahkan jika pada awalnya tidak selaras dengan kerangka logis Anda sendiri.\"],\"roleModel\":[\"Marie Curie\",\"Alan Turing\",\"Ruth Bader Ginsburg\",\"Sheryl Sandberg\",\"Elon Musk\"],\"shortSummary\":\"Individu ini secara fundamental adalah pemikir analitis yang didorong oleh rasa ingin tahu yang tinggi dan keinginan untuk memahami sistem yang kompleks. Mereka memiliki kapasitas luar biasa untuk inovasi dan pemecahan masalah, tidak hanya secara teoritis tetapi juga dengan kemampuan untuk mengimplementasikan solusi yang efektif dan praktis. Disiplin, integritas, dan ketahanan adalah ciri khas yang menopang pendekatan mereka dalam menghadapi tantangan.\",\"strengths\":[\"Kemampuan analisis dan pemecahan masalah yang luar biasa.\",\"Dorongan kuat untuk pembelajaran berkelanjutan dan eksplorasi intelektual.\",\"Kapasitas tinggi untuk inovasi dan pemikiran kreatif.\",\"Ketahanan dan ketekunan dalam menghadapi rintangan.\",\"Integritas dan komitmen terhadap prinsip etika.\"],\"weaknesses\":[\"Potensi untuk bersikap terlalu lugas atau blak-blakan dalam interaksi interpersonal.\",\"Cenderung kurang nyaman dalam lingkungan yang sangat tidak terstruktur atau terlalu berorientasi sosial.\",\"Mungkin terlalu fokus pada detail, berpotensi memperlambat pengambilan keputusan di situasi yang membutuhkan kecepatan.\",\"Kurang sesuai untuk peran yang menuntut tingkat empati emosional yang tinggi atau konsensus interpersonal yang konstan.\"],\"workEnvironment\":\"Lingkungan kerja ideal adalah tempat yang mempromosikan pemikiran mendalam, analisis data, dan inovasi, namun tetap menyediakan struktur dan kejelasan tujuan. Kolaborasi dihargai dalam konteks penyelesaian tugas dan proyek, bukan semata-mata interaksi sosial yang ekstensif. Penting bagi mereka untuk berada di tempat yang menghargai keahlian, keputusan berbasis data, serta menjunjung tinggi etika dan integritas dalam praktik profesional.\"}","completed","2025-07-17 04:29:14.366 +00:00","2025-07-17 04:29:14.366 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:29:14:2914"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:29:14:2914","userId":"e9034651-8aa9-471b-97c7-71babc1ac589"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:29:14:2914"}
{"level":"error","message":"Database insert error details: insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(e9034651-8aa9-471b-97c7-71babc1ac589) is not present in table \"users\".","message":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","table":"analysis_results"},"parameters":["7ef131cc-46f3-4f23-b9c3-e9a07e980dcb","e9034651-8aa9-471b-97c7-71babc1ac589","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":90,\"judgment\":75,\"loveOfLearning\":80,\"perspective\":70,\"bravery\":65,\"perseverance\":85,\"honesty\":90,\"zest\":75,\"love\":80,\"kindness\":85,\"socialIntelligence\":70,\"teamwork\":80,\"fairness\":85,\"leadership\":75,\"forgiveness\":70,\"humility\":65,\"prudence\":75,\"selfRegulation\":80,\"appreciationOfBeauty\":70,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Principled Investigator\",\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Research & Development Engineer\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Management Consultant (Strategy/Operations)\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Cybersecurity Analyst\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Systems Architect\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}}],\"insights\":[\"Prioritaskan pengembangan kemampuan komunikasi persuasif untuk menyeimbangkan kecenderungan direktif Anda, terutama dalam konteks kolaborasi antar tim.\",\"Cari lingkungan kerja yang memfasilitasi eksplorasi intelektual mendalam dan implementasi solusi praktis, bukan hanya teori semata.\",\"Manfaatkan rasa ingin tahu dan kecintaan Anda pada pembelajaran untuk terus menguasai bidang-bidang baru yang kompleks dan multidisiplin.\",\"Ambil peran kepemimpinan yang membutuhkan analisis kritis, pemecahan masalah yang inovatif, dan komitmen kuat terhadap prinsip etika.\",\"Latih diri untuk menerima dan mengintegrasikan perspektif yang berbeda, bahkan jika pada awalnya tidak selaras dengan kerangka logis Anda sendiri.\"],\"roleModel\":[\"Marie Curie\",\"Alan Turing\",\"Ruth Bader Ginsburg\",\"Sheryl Sandberg\",\"Elon Musk\"],\"shortSummary\":\"Individu ini secara fundamental adalah pemikir analitis yang didorong oleh rasa ingin tahu yang tinggi dan keinginan untuk memahami sistem yang kompleks. Mereka memiliki kapasitas luar biasa untuk inovasi dan pemecahan masalah, tidak hanya secara teoritis tetapi juga dengan kemampuan untuk mengimplementasikan solusi yang efektif dan praktis. Disiplin, integritas, dan ketahanan adalah ciri khas yang menopang pendekatan mereka dalam menghadapi tantangan.\",\"strengths\":[\"Kemampuan analisis dan pemecahan masalah yang luar biasa.\",\"Dorongan kuat untuk pembelajaran berkelanjutan dan eksplorasi intelektual.\",\"Kapasitas tinggi untuk inovasi dan pemikiran kreatif.\",\"Ketahanan dan ketekunan dalam menghadapi rintangan.\",\"Integritas dan komitmen terhadap prinsip etika.\"],\"weaknesses\":[\"Potensi untuk bersikap terlalu lugas atau blak-blakan dalam interaksi interpersonal.\",\"Cenderung kurang nyaman dalam lingkungan yang sangat tidak terstruktur atau terlalu berorientasi sosial.\",\"Mungkin terlalu fokus pada detail, berpotensi memperlambat pengambilan keputusan di situasi yang membutuhkan kecepatan.\",\"Kurang sesuai untuk peran yang menuntut tingkat empati emosional yang tinggi atau konsensus interpersonal yang konstan.\"],\"workEnvironment\":\"Lingkungan kerja ideal adalah tempat yang mempromosikan pemikiran mendalam, analisis data, dan inovasi, namun tetap menyediakan struktur dan kejelasan tujuan. Kolaborasi dihargai dalam konteks penyelesaian tugas dan proyek, bukan semata-mata interaksi sosial yang ekstensif. Penting bagi mereka untuk berada di tempat yang menghargai keahlian, keputusan berbasis data, serta menjunjung tinggi etika dan integritas dalam praktik profesional.\"}","completed","2025-07-17 04:29:24.410 +00:00","2025-07-17 04:29:24.410 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:29:24:2924"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:29:24:2924","userId":"e9034651-8aa9-471b-97c7-71babc1ac589"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:29:24:2924"}
{"level":"error","message":"Database insert error details: insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(e9034651-8aa9-471b-97c7-71babc1ac589) is not present in table \"users\".","message":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","table":"analysis_results"},"parameters":["2c4524e0-cca4-4780-9b60-e19875863cb4","e9034651-8aa9-471b-97c7-71babc1ac589","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":90,\"judgment\":75,\"loveOfLearning\":80,\"perspective\":70,\"bravery\":65,\"perseverance\":85,\"honesty\":90,\"zest\":75,\"love\":80,\"kindness\":85,\"socialIntelligence\":70,\"teamwork\":80,\"fairness\":85,\"leadership\":75,\"forgiveness\":70,\"humility\":65,\"prudence\":75,\"selfRegulation\":80,\"appreciationOfBeauty\":70,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Principled Investigator\",\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Research & Development Engineer\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Management Consultant (Strategy/Operations)\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Cybersecurity Analyst\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Systems Architect\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}}],\"insights\":[\"Prioritaskan pengembangan kemampuan komunikasi persuasif untuk menyeimbangkan kecenderungan direktif Anda, terutama dalam konteks kolaborasi antar tim.\",\"Cari lingkungan kerja yang memfasilitasi eksplorasi intelektual mendalam dan implementasi solusi praktis, bukan hanya teori semata.\",\"Manfaatkan rasa ingin tahu dan kecintaan Anda pada pembelajaran untuk terus menguasai bidang-bidang baru yang kompleks dan multidisiplin.\",\"Ambil peran kepemimpinan yang membutuhkan analisis kritis, pemecahan masalah yang inovatif, dan komitmen kuat terhadap prinsip etika.\",\"Latih diri untuk menerima dan mengintegrasikan perspektif yang berbeda, bahkan jika pada awalnya tidak selaras dengan kerangka logis Anda sendiri.\"],\"roleModel\":[\"Marie Curie\",\"Alan Turing\",\"Ruth Bader Ginsburg\",\"Sheryl Sandberg\",\"Elon Musk\"],\"shortSummary\":\"Individu ini secara fundamental adalah pemikir analitis yang didorong oleh rasa ingin tahu yang tinggi dan keinginan untuk memahami sistem yang kompleks. Mereka memiliki kapasitas luar biasa untuk inovasi dan pemecahan masalah, tidak hanya secara teoritis tetapi juga dengan kemampuan untuk mengimplementasikan solusi yang efektif dan praktis. Disiplin, integritas, dan ketahanan adalah ciri khas yang menopang pendekatan mereka dalam menghadapi tantangan.\",\"strengths\":[\"Kemampuan analisis dan pemecahan masalah yang luar biasa.\",\"Dorongan kuat untuk pembelajaran berkelanjutan dan eksplorasi intelektual.\",\"Kapasitas tinggi untuk inovasi dan pemikiran kreatif.\",\"Ketahanan dan ketekunan dalam menghadapi rintangan.\",\"Integritas dan komitmen terhadap prinsip etika.\"],\"weaknesses\":[\"Potensi untuk bersikap terlalu lugas atau blak-blakan dalam interaksi interpersonal.\",\"Cenderung kurang nyaman dalam lingkungan yang sangat tidak terstruktur atau terlalu berorientasi sosial.\",\"Mungkin terlalu fokus pada detail, berpotensi memperlambat pengambilan keputusan di situasi yang membutuhkan kecepatan.\",\"Kurang sesuai untuk peran yang menuntut tingkat empati emosional yang tinggi atau konsensus interpersonal yang konstan.\"],\"workEnvironment\":\"Lingkungan kerja ideal adalah tempat yang mempromosikan pemikiran mendalam, analisis data, dan inovasi, namun tetap menyediakan struktur dan kejelasan tujuan. Kolaborasi dihargai dalam konteks penyelesaian tugas dan proyek, bukan semata-mata interaksi sosial yang ekstensif. Penting bagi mereka untuk berada di tempat yang menghargai keahlian, keputusan berbasis data, serta menjunjung tinggi etika dan integritas dalam praktik profesional.\"}","completed","2025-07-17 04:29:44.431 +00:00","2025-07-17 04:29:44.431 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:29:44:2944"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:29:44:2944","userId":"e9034651-8aa9-471b-97c7-71babc1ac589"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:29:44:2944"}
{"level":"error","message":"Database insert error details: insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(e9034651-8aa9-471b-97c7-71babc1ac589) is not present in table \"users\".","message":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","table":"analysis_results"},"parameters":["636eb69b-b3be-4dc9-939c-a4139d28a802","e9034651-8aa9-471b-97c7-71babc1ac589","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":90,\"judgment\":75,\"loveOfLearning\":80,\"perspective\":70,\"bravery\":65,\"perseverance\":85,\"honesty\":90,\"zest\":75,\"love\":80,\"kindness\":85,\"socialIntelligence\":70,\"teamwork\":80,\"fairness\":85,\"leadership\":75,\"forgiveness\":70,\"humility\":65,\"prudence\":75,\"selfRegulation\":80,\"appreciationOfBeauty\":70,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Strategic Problem-Solver\",\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Research & Development Engineer\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Systems Architect\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Management Consultant\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}}],\"insights\":[\"Cultivate communication strategies that balance directness with diplomatic delivery, particularly in collaborative settings.\",\"Seek roles that provide continuous intellectual challenge and opportunities for autonomous problem-solving within defined frameworks.\",\"Actively seek out structured collaborative projects to develop adaptability in team dynamics.\",\"Engage in activities that broaden perspective beyond pure logic, such as interdisciplinary studies or empathy-building exercises.\",\"Leverage inherent integrity and perseverance to lead by example in ethical conduct and project execution.\"],\"roleModel\":[\"Elon Musk\",\"Marie Curie\",\"Bill Gates\",\"Sheryl Sandberg\"],\"shortSummary\":\"Individu ini dicirikan oleh kombinasi kuat antara keingintahuan intelektual dan kemampuan analisis yang mendalam. Dengan kecenderungan tinggi pada investigasi dan keterbukaan terhadap ide baru, ia didorong untuk memahami sistem kompleks dan mengembangkan solusi inovatif. Kekuatan inti terletak pada pemikiran kritis, integritas, dan ketekunan dalam mengejar tujuan. Meskipun memiliki preferensi untuk kemandirian dan pendekatan logis, terdapat potensi untuk mengoptimalkan interaksi dalam lingkungan kolaboratif.\",\"strengths\":[\"Superior analytical and critical thinking abilities, driven by an insatiable curiosity.\",\"Profound capacity for innovation and developing novel solutions.\",\"Exceptional integrity and adherence to ethical principles.\",\"High perseverance and self-regulation ensure diligent completion of complex tasks.\",\"A strong drive to translate theoretical understanding into practical, actionable outcomes.\"],\"weaknesses\":[\"Tendency towards directness in communication, which may be perceived as blunt due to lower Agreeableness.\",\"Potential discomfort in highly ambiguous or excessively social/unstructured environments.\",\"May over-prioritize logic, potentially overlooking interpersonal dynamics or emotional considerations.\",\"Preference for independent work may limit exposure to diverse collaborative methodologies.\"],\"workEnvironment\":\"Lingkungan yang ideal adalah yang menghargai ketelitian intelektual, memungkinkan pemecahan masalah secara mandiri, dan memberikan kontribusi berdasarkan meritokrasi. Lingkungan tersebut harus menawarkan tujuan yang jelas, peluang untuk pekerjaan analitis mendalam, dan akses ke data atau sistem yang kompleks. Meskipun kolaborasi dapat diterima, tuntutan sosial yang berlebihan atau struktur yang sangat ambigu mungkin kontraproduktif.\"}","completed","2025-07-17 04:30:13.770 +00:00","2025-07-17 04:30:13.770 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:30:13:3013"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:30:13:3013","userId":"e9034651-8aa9-471b-97c7-71babc1ac589"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:30:13:3013"}
{"level":"error","message":"Database insert error details: insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(e9034651-8aa9-471b-97c7-71babc1ac589) is not present in table \"users\".","message":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","table":"analysis_results"},"parameters":["649e7aea-3b39-4ee5-925e-42c32d7f8c68","e9034651-8aa9-471b-97c7-71babc1ac589","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":90,\"judgment\":75,\"loveOfLearning\":80,\"perspective\":70,\"bravery\":65,\"perseverance\":85,\"honesty\":90,\"zest\":75,\"love\":80,\"kindness\":85,\"socialIntelligence\":70,\"teamwork\":80,\"fairness\":85,\"leadership\":75,\"forgiveness\":70,\"humility\":65,\"prudence\":75,\"selfRegulation\":80,\"appreciationOfBeauty\":70,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Strategic Problem-Solver\",\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"jobAvailability\":\"super high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Research & Development Engineer\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"skillDevelopment\":\"super high\"}},{\"careerName\":\"Systems Architect\",\"careerProspect\":{\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Management Consultant\",\"careerProspect\":{\"careerProgression\":\"super high\",\"industryGrowth\":\"high\",\"jobAvailability\":\"high\",\"salaryPotential\":\"super high\",\"skillDevelopment\":\"super high\"}}],\"insights\":[\"Cultivate communication strategies that balance directness with diplomatic delivery, particularly in collaborative settings.\",\"Seek roles that provide continuous intellectual challenge and opportunities for autonomous problem-solving within defined frameworks.\",\"Actively seek out structured collaborative projects to develop adaptability in team dynamics.\",\"Engage in activities that broaden perspective beyond pure logic, such as interdisciplinary studies or empathy-building exercises.\",\"Leverage inherent integrity and perseverance to lead by example in ethical conduct and project execution.\"],\"roleModel\":[\"Elon Musk\",\"Marie Curie\",\"Bill Gates\",\"Sheryl Sandberg\"],\"shortSummary\":\"Individu ini dicirikan oleh kombinasi kuat antara keingintahuan intelektual dan kemampuan analisis yang mendalam. Dengan kecenderungan tinggi pada investigasi dan keterbukaan terhadap ide baru, ia didorong untuk memahami sistem kompleks dan mengembangkan solusi inovatif. Kekuatan inti terletak pada pemikiran kritis, integritas, dan ketekunan dalam mengejar tujuan. Meskipun memiliki preferensi untuk kemandirian dan pendekatan logis, terdapat potensi untuk mengoptimalkan interaksi dalam lingkungan kolaboratif.\",\"strengths\":[\"Superior analytical and critical thinking abilities, driven by an insatiable curiosity.\",\"Profound capacity for innovation and developing novel solutions.\",\"Exceptional integrity and adherence to ethical principles.\",\"High perseverance and self-regulation ensure diligent completion of complex tasks.\",\"A strong drive to translate theoretical understanding into practical, actionable outcomes.\"],\"weaknesses\":[\"Tendency towards directness in communication, which may be perceived as blunt due to lower Agreeableness.\",\"Potential discomfort in highly ambiguous or excessively social/unstructured environments.\",\"May over-prioritize logic, potentially overlooking interpersonal dynamics or emotional considerations.\",\"Preference for independent work may limit exposure to diverse collaborative methodologies.\"],\"workEnvironment\":\"Lingkungan yang ideal adalah yang menghargai ketelitian intelektual, memungkinkan pemecahan masalah secara mandiri, dan memberikan kontribusi berdasarkan meritokrasi. Lingkungan tersebut harus menawarkan tujuan yang jelas, peluang untuk pekerjaan analitis mendalam, dan akses ke data atau sistem yang kompleks. Meskipun kolaborasi dapat diterima, tuntutan sosial yang berlebihan atau struktur yang sangat ambigu mungkin kontraproduktif.\"}","completed","2025-07-17 04:30:18.821 +00:00","2025-07-17 04:30:18.821 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:30:18:3018"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:30:18:3018","userId":"e9034651-8aa9-471b-97c7-71babc1ac589"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:30:18:3018"}
{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:132:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-17 11:40:05:405"}
{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:134:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-18 05:35:13:3513"}
{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:134:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-18 05:59:11:5911"}
{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:134:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-18 07:03:05:35"}
{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:134:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-18 07:05:21:521"}
{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:134:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-18 08:02:36:236"}
{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:134:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-18 08:03:04:34"}
{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:134:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-18 08:10:37:1037"}
{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:134:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-18 08:11:06:116"}
{"database":"atma_db","error":"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-19 04:02:07:27"}
{"database":"atma_db","error":"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-19 04:03:34:334"}
{"database":"atma_db","error":"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-19 04:03:52:352"}
{"count":20,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-19 04:44:34:4434"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"7e49b54d-a006-48a1-957c-5447bb050049"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"45dff254-ba35-47c2-97aa-3b7525590ab4"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":2,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"5f5f81e3-0705-4ff6-a08d-34b53c16378b"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":3,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"cbb7c30f-842c-4c4b-acd6-7e2211d3d320"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":4,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"06181d17-a349-4ac8-b272-29c588c50b20"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":5,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"d4afbac5-aad5-4eda-8680-4a2842e1b010"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":6,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"f3e2da18-68e9-4a3b-810f-c5490b512fb8"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":7,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"c478bc43-d9be-4003-a61d-81483a472ad7"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":8,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"1123c6a1-63fc-4c65-8ba3-31b62ecb7485"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":9,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"5ea78b33-eeba-42f7-bddc-479942debb28"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":10,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"1662a1e4-3f2d-4c78-9b41-37c9c30761ad"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":11,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"3ea16d00-13a7-4078-bf51-8bd6c0ba9915"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":12,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"0329b899-5a6d-480b-b68e-16cb1e11a67b"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":13,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"12f137a6-b705-4daa-b335-4851c992a43e"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":14,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"6d24f968-babf-49e6-9994-d786ef48e8fd"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":15,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"382b0085-67fe-44e6-891d-259fe744c246"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":16,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"dd95ec99-dac2-4b90-ab67-f848a6b5cb43"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":17,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"1f4ae85d-65c2-47fb-904a-d8756022a622"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":18,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"90d7fcad-c2fb-4837-8bd7-b1b2c858fb78"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":19,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:34:4434","userId":"23cdf369-5fdd-4495-843d-56f0455e5fe2"}
{"count":10,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-19 04:44:38:4438"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"c211a64d-6048-4440-ae32-8aa735307902"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"58336b36-8a56-450d-ac0c-799d50d9cb47"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":2,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"f6824a23-7c56-4c2f-ae3c-3879af18e263"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":3,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"a5de8645-e005-4026-bb67-2a0594cc54d8"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":4,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"ed3337b4-aafc-4c71-a8cb-197669a1a009"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":5,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"ef070185-1435-470f-9860-f39034f66355"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":6,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"45179567-e37a-4eba-8cad-7abe8898e4d1"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":7,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"b8546ed2-1058-4a80-b4dc-d44645efd338"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":8,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"a4b68d29-3479-4abe-ac09-683919b6692c"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":9,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"3ddbaf46-1092-4206-b5a7-be861d1e8481"}
{"count":10,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-19 04:44:38:4438"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"61a468f6-3b45-4420-b3e3-985b4e89daf3"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"9d705ec9-e7ab-49d6-938f-ede0df71ab73"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":2,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"b56b1b26-ceec-40e4-b529-96a7aca326db"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":3,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"55e3bb4a-023d-4595-9a5c-7251b88c8753"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":4,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"b48604d3-f173-49be-b0b5-0deae0ba6961"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":5,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"941966b0-32bf-4500-92da-551e653bfd4b"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":6,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"3b35416a-e899-4a3a-8bee-f95b7b7d8dbb"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":7,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"adf9102b-8736-4de9-a9ca-32d39df405ac"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":8,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"9593d595-424f-4603-ae5a-a68ce0c13099"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":9,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:38:4438","userId":"0f00dfa5-8c26-40ad-a835-b0dda6884cfa"}
{"count":10,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-19 04:44:42:4442"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:42:4442","userId":"d6e67fe7-bd16-4403-9f85-756e54cfb0d9"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:42:4442","userId":"0fab7318-4a89-43ab-8251-ca6db502a08e"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":2,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:42:4442","userId":"7a58c464-898d-4275-94b6-3e4708981d07"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":3,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:42:4442","userId":"60f70834-0670-4612-9f1f-55976fd05ccc"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":4,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:42:4442","userId":"5629836b-f2af-4325-bf25-e1e141480836"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":5,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:42:4442","userId":"3600d3e4-e3bc-4b2c-ba76-b9c621e4589d"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":6,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:42:4442","userId":"d9b697e6-1526-4184-9692-2435d48d34ee"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":7,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:42:4442","userId":"cf287bf2-7461-4f6a-8db1-c0f689c47aef"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":8,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:42:4442","userId":"5a5d8185-7fbd-42a3-891e-da389ef716b9"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":9,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:42:4442","userId":"036343cd-9585-40a7-ad1e-2d79621214c6"}
{"count":20,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-19 04:44:44:4444"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"77a8e94f-1439-454f-8b98-f1c557e55461"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"e128aed6-e974-4f8b-b552-2d7b7454c427"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":2,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"58d2934e-93cd-4a01-a474-13a6fd47831e"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":3,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"58be7ce2-d3fb-4627-b6ee-ad06a917b627"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":4,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"361e656a-5383-420c-a2d8-8ba8c24ca0bd"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":5,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"448b44fa-ee03-42bf-862f-c99f0a65175f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":6,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"1fdbac12-d395-4595-a2f3-82ddcbd926db"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":7,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"cc590cad-7411-4590-be7f-d8608050b8d9"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":8,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"8fb8517a-70d1-43aa-ab66-a78cf27bc206"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":9,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"67ce8714-23aa-4eff-819b-1ebe4a801911"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":10,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"f990ae12-cbb7-45b8-b127-837ee06f57ef"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":11,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"1d64c8a4-8602-4b0f-bca2-bd7d0d25c1a0"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":12,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"d9f2dc8a-5335-4fcc-b4eb-5480e52b4aba"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":13,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"fceb86e5-c70a-4a61-bcd4-7960c7487f20"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":14,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"1bc6f2ed-a15b-45ff-a95e-a76e7d43bf2e"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":15,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"cc71c90b-f74a-4221-bbae-83faaf9c3bba"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":16,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"43b3a045-e01f-481f-a2fd-91384e965c50"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":17,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"f6416d9e-fb19-4b8d-b50d-82ac478959d9"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":18,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"e93b58a1-11d1-4daa-8f12-9bd5e5678c0f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":19,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:44:4444","userId":"4971ee6b-2326-4aad-9367-004453217d94"}
{"count":10,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-19 04:44:48:4448"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:48:4448","userId":"bd2e8229-8da7-4a4e-96de-f45f7095b981"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:48:4448","userId":"6639fe59-8a4d-4626-b04c-34611a1ea867"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":2,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:48:4448","userId":"7172d5ba-876c-401b-86b9-cd49474d632a"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":3,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:48:4448","userId":"10d4b551-67e8-4128-a7f5-d29b7843f3e0"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":4,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:48:4448","userId":"c869e7e8-012c-4093-93fe-d000a1a2e6b3"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":5,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:48:4448","userId":"fd48ea6c-6e77-4945-9f18-c0a98354080e"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":6,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:48:4448","userId":"95718f3a-2e8c-4fef-aade-fd6b43747b1c"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":7,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:48:4448","userId":"abf81515-2aee-4a94-8744-9402e3aef5be"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":8,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:48:4448","userId":"f1c33912-492b-45a1-accc-019ed2eb4510"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":9,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:48:4448","userId":"75ae73c6-e078-461e-a46e-dcec6c9d8a9f"}
{"count":10,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-19 04:44:50:4450"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:50:4450","userId":"3cc99fda-8d68-425d-ab45-daf98af24f07"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:50:4450","userId":"e69e11b1-a514-46ab-8ce3-2ce1aeeadf58"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":2,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:50:4450","userId":"65639bf2-9c7b-422d-bdb4-d60b096ab869"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":3,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:50:4450","userId":"7ce922a8-8e1e-4fc8-86c1-94021cb195e8"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":4,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:50:4450","userId":"8ff43c56-eabe-4a6d-b5e1-0c302c444f44"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":5,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:50:4450","userId":"d475f0f8-b6f3-471f-bcb6-b6e4ab446365"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":6,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:50:4450","userId":"4f0a8cb1-425e-4473-bbca-bde065d94d16"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":7,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:50:4450","userId":"d523fa27-fcee-4ea1-ade2-d1f6add68ef2"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":8,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:50:4450","userId":"70f51d3c-9fd1-49e4-976e-c3a00881ba24"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":9,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:50:4450","userId":"7cc8c2b8-c9e9-4676-8d9b-48cb1a479430"}
{"count":10,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-19 04:44:52:4452"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:52:4452","userId":"66cd9b5d-a29c-4b73-b984-644ba3fc8ecd"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:52:4452","userId":"e5c37e76-2872-49ea-bc7a-ea3fd5b7c053"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":2,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:52:4452","userId":"8255c864-bd48-47ab-ae18-fddb1bd40fd4"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":3,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:52:4452","userId":"09880e58-9485-4a8c-9280-668019c9807a"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":4,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:52:4452","userId":"8be00145-910d-4797-965d-e7fb0983a1c1"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":5,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:52:4452","userId":"48c76014-531a-47cb-ab88-47668202155b"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":6,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:52:4452","userId":"6d9489c9-7b90-40b5-a04d-6fcaf562020c"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":7,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:52:4452","userId":"34277e8b-fc81-4489-8857-dafb56b112d7"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":8,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:52:4452","userId":"96b96fc0-ddf7-4c81-9131-56953d50b0b5"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":9,"level":"error","message":"Individual insert failed","timestamp":"2025-07-19 04:44:52:4452","userId":"ec73c5f9-6be8-4ef5-b329-a965b14c593b"}
