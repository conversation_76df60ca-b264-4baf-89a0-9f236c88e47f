# Environment
NODE_ENV=development

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
QUEUE_NAME=assessment_analysis
EXCHANGE_NAME=atma_exchange
ROUTING_KEY=analysis.process
DEAD_LETTER_QUEUE=assessment_analysis_dlq

# Queue Configuration
QUEUE_DURABLE=true
MESSAGE_PERSISTENT=true

# Google Generative AI Configuration
GOOGLE_AI_API_KEY=your_google_ai_api_key_here_get_from_google_ai_studio
GOOGLE_AI_MODEL=gemini-2.5-flash
AI_TEMPERATURE=0.7
AI_MAX_TOKENS=4096

# Mock AI Configuration (for testing without using paid Gemini API)
# Set to 'true' to use mock AI responses instead of real Gemini API
USE_MOCK_MODEL=false

# Archive Service Configuration
ARCHIVE_SERVICE_URL=http://localhost:3002
# Use the same key as other services for internal communication
ARCHIVE_SERVICE_KEY=internal_service_secret_key_change_in_production

# Notification Service Configuration (Optional - if notification service is implemented)
NOTIFICATION_SERVICE_URL=http://localhost:3005
# Use the same key as other services for internal communication
NOTIFICATION_SERVICE_KEY=internal_service_secret_key_change_in_production

# Assessment Service Configuration
ASSESSMENT_SERVICE_URL=http://localhost:3003
# Use the same key as other services for internal communication
ASSESSMENT_SERVICE_KEY=internal_service_secret_key_change_in_production

# Worker Configuration
WORKER_CONCURRENCY=10
MAX_RETRIES=3
RETRY_DELAY=5000
PROCESSING_TIMEOUT=300000
HEARTBEAT_INTERVAL=30000
HEALTH_CHECK_INTERVAL=60000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/analysis-worker.log
